import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import Constants from 'expo-constants';
import api from './api';
import { getDeviceLanguage } from './i18nService';
import * as Sentry from '@sentry/react-native';
import { SERVICE_CACHE_DURATIONS, DURATION_HELPERS, HASH_HELPERS } from '../config/cacheConfig';

// Types
export interface WhatsNewLog {
  id: string;
  type: 'dark' | 'red' | 'cyan' | 'purple';
  date: string;
  title_en: string;
  title_es: string;
  title_dom: string;
  description_en: string;
  description_es: string;
  description_dom: string;
  videoUrl?: string;
  appVersion: string;
  platform: 'ios' | 'android' | 'both';
  forceUpdate?: boolean; // Whether this update is mandatory
  createdAt: string;
  updatedAt: string;
  updateApp?: boolean; // Added for mobile app logic
  // Computed fields for mobile app
  title?: string; // Localized title based on user's language
  description?: string; // Localized description based on user's language
}

// Storage keys
const WHATSNEW_STORAGE_KEY = 'taptrap_whatsnew_logs';
const WHATSNEW_LAST_VISIT_KEY = 'taptrap_whatsnew_last_visit';
const WHATSNEW_CACHE_TIMESTAMP_KEY = 'taptrap_whatsnew_cache_timestamp';
const WHATSNEW_CHANGE_CHECK_KEY = 'taptrap_whatsnew_change_check';
const WHATSNEW_CHANGE_HASH_KEY = 'taptrap_whatsnew_change_hash';
const WHATSNEW_NEW_LOGS_CHECK_KEY = 'taptrap_whatsnew_new_logs_check'; // For red dot throttling

// Cache duration from centralized configuration - smart checking every 12 hours
const CONTENT_CHECK_DURATION = SERVICE_CACHE_DURATIONS.WHATS_NEW;

/**
 * Get current app language from AsyncStorage or fallback to device language
 */
const getCurrentAppLanguage = async (): Promise<'en' | 'es' | 'dom'> => {
  try {
    const storedLanguage = await AsyncStorage.getItem('appLanguage');
    if (storedLanguage && ['en', 'es', 'dom'].includes(storedLanguage)) {
      return storedLanguage as 'en' | 'es' | 'dom';
    }
    // Fall back to device language
    return getDeviceLanguage();
  } catch (error) {
    console.error('Error getting current app language:', error);
    Sentry.captureException(error);
    return 'en'; // Default to English
  }
};

/**
 * Get localized text based on user's language preference
 */
const getLocalizedText = async (textEn: string, textEs: string, textDom: string): Promise<string> => {
  const language = await getCurrentAppLanguage();
  if (language === 'es') return textEs;
  if (language === 'dom') return textDom;
  return textEn; // Default to English
};

/**
 * Process logs to add localized title and description
 */
const processLogsForMobile = async (logs: WhatsNewLog[]): Promise<WhatsNewLog[]> => {
  const processedLogs = await Promise.all(logs.map(async log => ({
    ...log,
    title: await getLocalizedText(log.title_en, log.title_es, log.title_dom),
    description: await getLocalizedText(log.description_en, log.description_es, log.description_dom)
  })));
  return processedLogs;
};

/**
 * Get current app version from Expo config
 */
const getCurrentAppVersion = (): string => {
  return Constants.expoConfig?.version || '1.0.0';
};

/**
 * Compare two semantic versions
 * Returns true if currentVersion is less than requiredVersion
 */
const isVersionLower = (currentVersion: string, requiredVersion: string): boolean => {
  const parseVersion = (version: string) => {
    return version.split('.').map(num => parseInt(num, 10));
  };

  const current = parseVersion(currentVersion);
  const required = parseVersion(requiredVersion);

  for (let i = 0; i < Math.max(current.length, required.length); i++) {
    const currentPart = current[i] || 0;
    const requiredPart = required[i] || 0;

    if (currentPart < requiredPart) return true;
    if (currentPart > requiredPart) return false;
  }

  return false; // Versions are equal
};

// Cache expiration check removed - now using smart change detection every 12 hours

/**
 * Save What's New logs to storage
 */
const saveLogsToStorage = async (logs: WhatsNewLog[]): Promise<void> => {
  try {
    const dataToStore = JSON.stringify(logs);
    await AsyncStorage.setItem(WHATSNEW_STORAGE_KEY, dataToStore);
    await AsyncStorage.setItem(WHATSNEW_CACHE_TIMESTAMP_KEY, Date.now().toString());
    console.log('What\'s New logs saved to storage');
  } catch (error) {
    console.error('Error saving What\'s New logs to storage:', error);
    Sentry.captureException(error);
  }
};

/**
 * Get cached What's New logs from storage
 */
const getCachedLogs = async (): Promise<WhatsNewLog[]> => {
  try {
    const storedData = await AsyncStorage.getItem(WHATSNEW_STORAGE_KEY);
    if (!storedData) return [];

    const logs = JSON.parse(storedData) as WhatsNewLog[];
    console.log(`Retrieved ${logs.length} cached What's New logs`);
    return logs;
  } catch (error) {
    console.error('Error retrieving cached What\'s New logs:', error);
    Sentry.captureException(error);
    return [];
  }
};

/**
 * Fetch What's New logs from API
 */
const fetchLogsFromAPI = async (): Promise<WhatsNewLog[] | null> => {
  try {
    console.log('Fetching What\'s New logs from API...');
    const response = await api.get('/whatsnew');

    if (response.data && Array.isArray(response.data)) {
      console.log(`Fetched ${response.data.length} What's New logs from API`);
      return response.data;
    }

    console.warn('Invalid API response format for What\'s New logs');
    return null;
  } catch (error) {
    console.error('Error fetching What\'s New logs from API:', error);
    Sentry.captureException(error);
    return null;
  }
};

/**
 * Check internet connectivity
 */
const isInternetAvailable = async (): Promise<boolean> => {
  try {
    const networkState = await NetInfo.fetch();
    return networkState.isConnected === true;
  } catch (error) {
    console.error('Error checking internet connectivity:', error);
    Sentry.captureException(error);
    return false;
  }
};

/**
 * Get the timestamp of the last What's New change check
 */
const getLastWhatsNewChangeCheckTime = async (): Promise<number> => {
  try {
    const storedTimestamp = await AsyncStorage.getItem(WHATSNEW_CHANGE_CHECK_KEY);
    if (!storedTimestamp) {
      return 0;
    }

    return parseInt(storedTimestamp, 10);
  } catch (error) {
    console.error('Error getting last What\'s New change check time:', error);
    Sentry.captureException(error);
    return 0;
  }
};

/**
 * Update the timestamp of the last What's New change check
 */
const updateLastWhatsNewChangeCheckTime = async (): Promise<boolean> => {
  try {
    const now = Date.now();
    await AsyncStorage.setItem(WHATSNEW_CHANGE_CHECK_KEY, now.toString());
    return true;
  } catch (error) {
    console.error('Error updating last What\'s New change check time:', error);
    Sentry.captureException(error);
    return false;
  }
};

/**
 * Get stored What's New change hash
 */
const getStoredWhatsNewChangeHash = async (): Promise<string | null> => {
  try {
    return await AsyncStorage.getItem(WHATSNEW_CHANGE_HASH_KEY);
  } catch (error) {
    console.error('Error getting stored What\'s New change hash:', error);
    Sentry.captureException(error);
    return null;
  }
};

/**
 * Store What's New change hash
 */
const storeWhatsNewChangeHash = async (hash: string): Promise<boolean> => {
  try {
    await AsyncStorage.setItem(WHATSNEW_CHANGE_HASH_KEY, hash);
    return true;
  } catch (error) {
    console.error('Error storing What\'s New change hash:', error);
    Sentry.captureException(error);
    return false;
  }
};

/**
 * Check if What's New logs have changed since last check (lightweight API call)
 * This is different from checkForNewLogs which is used for the red dot indicator
 */
const checkWhatsNewChanges = async (): Promise<{ hasChanges: boolean; newHash?: string }> => {
  try {
    console.log('🔍 Checking for What\'s New changes...');

    // Check if we've checked recently (within the last 12 hours)
    const now = Date.now();
    const lastCheckTime = await getLastWhatsNewChangeCheckTime();
    const timeSinceLastCheck = now - lastCheckTime;

    // TEMPORARY: Disable throttling for testing
    if (false && timeSinceLastCheck < CONTENT_CHECK_DURATION) {
      console.log(`⏰ What's New change check throttled (last check was ${DURATION_HELPERS.toHumanReadable(timeSinceLastCheck)} ago)`);
      return { hasChanges: false };
    }

    // Make lightweight API call to check for changes
    const response = await api.get('/whatsnew/check-changes', {
      timeout: 10000 // 10 second timeout for change check
    });

    if (response.data && response.data.hash) {
      const newHash = response.data.hash;
      const storedHash = await getStoredWhatsNewChangeHash();

      console.log('📊 What\'s New change check result:', {
        newHash: newHash.substring(0, 8) + '...',
        storedHash: storedHash ? storedHash.substring(0, 8) + '...' : 'none',
        hasChanges: newHash !== storedHash
      });

      // Update last check time
      await updateLastWhatsNewChangeCheckTime();

      if (newHash !== storedHash) {
        console.log('✅ What\'s New changes detected');
        return { hasChanges: true, newHash };
      } else {
        console.log('📦 No What\'s New changes detected');
        return { hasChanges: false };
      }
    } else {
      console.warn('⚠️ Invalid response from What\'s New change check API');
      return { hasChanges: true }; // Assume changes if API response is invalid
    }
  } catch (error) {
    console.error('❌ Error checking What\'s New changes:', error);
    Sentry.captureException(error);
    // On error, assume changes to ensure What's New logs are fetched
    return { hasChanges: true };
  }
};

/**
 * Main function to fetch and update What's New logs
 * Similar pattern to fetchAndUpdateGameContent from contentService
 */
export const fetchAndUpdateWhatsNewLogs = async (forceRefresh: boolean = false): Promise<WhatsNewLog[]> => {
  try {
    console.log('🆕 Starting What\'s New logs fetch process...');

    // Check if we have internet connection
    const isOnline = await isInternetAvailable();
    
    if (!isOnline) {
      console.log('📱 No internet connection, using cached What\'s New logs');
      const cachedLogs = await getCachedLogs();
      return await processLogsForMobile(cachedLogs);
    }

    // Smart What's New checking - lightweight check every 12 hours, full fetch only if changes detected
    if (!forceRefresh) {
      try {
        const changeCheck = await checkWhatsNewChanges();
        if (!changeCheck.hasChanges) {
          console.log('🔍 No What\'s New changes detected, using cached logs');
          const cachedLogs = await getCachedLogs();
          const processedLogs = await processLogsForMobile(cachedLogs);
          console.log('📦 Using cached What\'s New logs after change check');
          return processedLogs;
        } else {
          console.log('🔄 What\'s New changes detected, proceeding with full refresh');
        }
      } catch (error) {
        console.error('❌ Error during What\'s New change detection, proceeding with full refresh:', error);
        Sentry.captureException(error);
      }
    } else {
      console.log('🔄 Force refresh requested, bypassing change detection');
    }

    // Try to fetch fresh logs from API
    const freshLogs = await fetchLogsFromAPI();

    // Check if API call was successful (freshLogs will be an array, even if empty, or null on error)
    if (freshLogs !== null) {
      console.log('✅ Fresh What\'s New logs fetched successfully');

      // Process logs for mobile app (add localized text and updateApp flag)
      const currentVersion = getCurrentAppVersion();
      const localizedLogs = await processLogsForMobile(freshLogs);
      const processedLogs = localizedLogs.map(log => ({
        ...log,
        updateApp: isVersionLower(currentVersion, log.appVersion)
      }));

      // Save to cache (even if empty array)
      await saveLogsToStorage(processedLogs);

      // Store What's New hash for change detection
      try {
        const logsHash = HASH_HELPERS.generateContentHash(processedLogs);
        await storeWhatsNewChangeHash(logsHash);
        console.log('✅ What\'s New change hash stored successfully');
      } catch (hashError) {
        console.warn('⚠️ Failed to store What\'s New change hash:', hashError);
      }

      return processedLogs;
    } else {
      console.log('❌ Failed to fetch fresh What\'s New logs, falling back to cache');
      const cachedLogs = await getCachedLogs();
      return await processLogsForMobile(cachedLogs);
    }
  } catch (error) {
    console.error('Error in fetchAndUpdateWhatsNewLogs:', error);
    Sentry.captureException(error);
    const cachedLogs = await getCachedLogs();
    return await processLogsForMobile(cachedLogs);
  }
};

/**
 * Check for new logs since last visit (for home screen dot indicator)
 * Now with 12-hour throttling to match other optimizations
 */
export const checkForNewLogs = async (): Promise<{ hasNewLogs: boolean; newLogsCount: number }> => {
  try {
    // Check if we have internet connection
    const isOnline = await isInternetAvailable();

    if (!isOnline) {
      console.log('📱 No internet connection, cannot check for new logs');
      return { hasNewLogs: false, newLogsCount: 0 };
    }

    // Check if we've checked recently (within the last 12 hours)
    const now = Date.now();
    const lastCheckTime = await AsyncStorage.getItem(WHATSNEW_NEW_LOGS_CHECK_KEY);

    if (lastCheckTime) {
      const timeSinceLastCheck = now - parseInt(lastCheckTime, 10);
      if (timeSinceLastCheck < CONTENT_CHECK_DURATION) {
        console.log(`⏰ What's New red dot check throttled (last check was ${DURATION_HELPERS.toHumanReadable(timeSinceLastCheck)} ago)`);
        // Return cached result or false to avoid API call
        return { hasNewLogs: false, newLogsCount: 0 };
      }
    }

    // Get last visit timestamp
    const lastVisit = await AsyncStorage.getItem(WHATSNEW_LAST_VISIT_KEY);

    console.log('🔍 Checking for new What\'s New logs since:', lastVisit || 'never');

    // Make lightweight API call
    const response = await api.get('/whatsnew/check-new', {
      params: lastVisit ? { lastVisit } : {}
    });

    const { hasNewLogs, newLogsCount } = response.data;

    console.log(`Found ${newLogsCount} new What's New logs`);

    // Update last check time
    await AsyncStorage.setItem(WHATSNEW_NEW_LOGS_CHECK_KEY, now.toString());

    return { hasNewLogs, newLogsCount };
  } catch (error) {
    console.error('Error checking for new What\'s New logs:', error);
    Sentry.captureException(error);
    return { hasNewLogs: false, newLogsCount: 0 };
  }
};

/**
 * Mark What's New as visited (removes red dot indicator)
 */
export const markWhatsNewAsVisited = async (): Promise<void> => {
  try {
    const now = new Date().toISOString();
    await AsyncStorage.setItem(WHATSNEW_LAST_VISIT_KEY, now);
    console.log('✅ What\'s New marked as visited:', now);
  } catch (error) {
    console.error('Error marking What\'s New as visited:', error);
    Sentry.captureException(error);
  }
};

/**
 * Get cached logs without making API calls (for immediate display)
 */
export const getCachedWhatsNewLogs = async (): Promise<WhatsNewLog[]> => {
  const cachedLogs = await getCachedLogs();
  // Process cached logs to ensure they have localized text
  return await processLogsForMobile(cachedLogs);
};

/**
 * Check if there are any logs that require an app update
 * Returns both whether an update is needed and if it's forced
 */
export const checkForAppUpdate = async (): Promise<{
  needsUpdate: boolean;
  isForced: boolean;
  updateLog?: WhatsNewLog
}> => {
  try {
    console.log('🔄 Checking for app updates...');

    // Get current logs (this will use cache if available or fetch fresh)
    const logs = await fetchAndUpdateWhatsNewLogs();

    // Find the first log that requires an update (logs are sorted by creation date, newest first)
    const updateLog = logs.find(log => log.updateApp === true);

    if (updateLog) {
      console.log('📱 Update required:', {
        version: updateLog.appVersion,
        isForced: updateLog.forceUpdate || false,
        title: updateLog.title
      });

      return {
        needsUpdate: true,
        isForced: updateLog.forceUpdate || false,
        updateLog
      };
    }

    console.log('✅ No app update required');
    return {
      needsUpdate: false,
      isForced: false
    };
  } catch (error) {
    console.error('Error checking for app update:', error);
    Sentry.captureException(error);
    return {
      needsUpdate: false,
      isForced: false
    };
  }
};
