/**
 * Ad Settings Service
 *
 * Manages fetching and caching of ad-related settings from the backend.
 * Follows the same pattern as contentService with time-based throttling.
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import api, { API_URL } from './api';
import * as Sentry from '@sentry/react-native';
import { SERVICE_CACHE_DURATIONS, DURATION_HELPERS, HASH_HELPERS } from '../config/cacheConfig';

const AD_SETTINGS_STORAGE_KEY = 'taptrap_ad_settings';
const LAST_AD_SETTINGS_REFRESH_KEY = 'taptrap_last_ad_settings_refresh';
const AD_SETTINGS_CHANGE_CHECK_KEY = 'taptrap_ad_settings_change_check';
const AD_SETTINGS_CHANGE_HASH_KEY = 'taptrap_ad_settings_change_hash';

// Default ad settings (used as fallback)
export interface AdSettings {
  watchAdsButtonEnabled: boolean; // Legacy field for backward compatibility
  watchAdsButtonEnabledIOS: boolean;
  watchAdsButtonEnabledAndroid: boolean;
  requiredAdsCount: number;
  freeContentLimit: number;
}

// No default values - everything must come from API

// Content check duration from centralized configuration - smart checking every 12 hours
const CONTENT_CHECK_DURATION = SERVICE_CACHE_DURATIONS.AD_SETTINGS;

// Global promise to prevent concurrent calls
let ongoingFetchPromise: Promise<AdSettings> | null = null;

/**
 * Get cached ad settings from AsyncStorage
 */
export const getCachedAdSettings = async (): Promise<AdSettings | null> => {
  try {
    if (Platform.OS === 'web') {
      console.log('Ad settings storage not available on web platform');
      return null;
    }

    const storedSettings = await AsyncStorage.getItem(AD_SETTINGS_STORAGE_KEY);

    if (!storedSettings) {
      console.log('No cached ad settings available');
      return null;
    }

    const settings = JSON.parse(storedSettings);
    console.log('Cached ad settings loaded:', settings);
    return settings;
  } catch (error) {
    console.error('Error reading cached ad settings:', error);
    Sentry.captureException(error);
    return null;
  }
};

/**
 * Save ad settings to AsyncStorage
 */
const saveAdSettingsToStorage = async (settings: AdSettings): Promise<boolean> => {
  try {
    if (Platform.OS === 'web') {
      return false;
    }

    await AsyncStorage.setItem(AD_SETTINGS_STORAGE_KEY, JSON.stringify(settings));
    console.log('Ad settings saved to storage:', settings);
    return true;
  } catch (error) {
    console.error('Error saving ad settings to storage:', error);
    Sentry.captureException(error);
    return false;
  }
};

/**
 * Get the timestamp of the last ad settings refresh
 */
export const getLastAdSettingsRefreshTime = async (): Promise<number> => {
  try {
    if (Platform.OS === 'web') {
      return 0;
    }

    const storedTimestamp = await AsyncStorage.getItem(LAST_AD_SETTINGS_REFRESH_KEY);
    if (!storedTimestamp) {
      return 0;
    }

    return parseInt(storedTimestamp, 10);
  } catch (error) {
    console.error('Error getting last ad settings refresh time:', error);
    Sentry.captureException(error);
    return 0;
  }
};

/**
 * Update the timestamp of the last ad settings refresh
 */
const updateLastAdSettingsRefreshTime = async (): Promise<boolean> => {
  try {
    if (Platform.OS === 'web') {
      return false;
    }

    const now = Date.now();
    await AsyncStorage.setItem(LAST_AD_SETTINGS_REFRESH_KEY, now.toString());
    return true;
  } catch (error) {
    console.error('Error updating last ad settings refresh time:', error);
    Sentry.captureException(error);
    return false;
  }
};

/**
 * Get the timestamp of the last ad settings change check
 */
const getLastAdSettingsChangeCheckTime = async (): Promise<number> => {
  try {
    if (Platform.OS === 'web') {
      return 0;
    }

    const storedTimestamp = await AsyncStorage.getItem(AD_SETTINGS_CHANGE_CHECK_KEY);
    if (!storedTimestamp) {
      return 0;
    }

    return parseInt(storedTimestamp, 10);
  } catch (error) {
    console.error('Error getting last ad settings change check time:', error);
    Sentry.captureException(error);
    return 0;
  }
};

/**
 * Update the timestamp of the last ad settings change check
 */
const updateLastAdSettingsChangeCheckTime = async (): Promise<boolean> => {
  try {
    if (Platform.OS === 'web') {
      return false;
    }

    const now = Date.now();
    await AsyncStorage.setItem(AD_SETTINGS_CHANGE_CHECK_KEY, now.toString());
    return true;
  } catch (error) {
    console.error('Error updating last ad settings change check time:', error);
    Sentry.captureException(error);
    return false;
  }
};

/**
 * Get stored ad settings change hash
 */
const getStoredAdSettingsChangeHash = async (): Promise<string | null> => {
  try {
    if (Platform.OS === 'web') {
      return null;
    }

    return await AsyncStorage.getItem(AD_SETTINGS_CHANGE_HASH_KEY);
  } catch (error) {
    console.error('Error getting stored ad settings change hash:', error);
    Sentry.captureException(error);
    return null;
  }
};

/**
 * Store ad settings change hash
 */
const storeAdSettingsChangeHash = async (hash: string): Promise<boolean> => {
  try {
    if (Platform.OS === 'web') {
      return false;
    }

    await AsyncStorage.setItem(AD_SETTINGS_CHANGE_HASH_KEY, hash);
    return true;
  } catch (error) {
    console.error('Error storing ad settings change hash:', error);
    Sentry.captureException(error);
    return false;
  }
};

/**
 * Check if ad settings have changed since last check (lightweight API call)
 */
const checkAdSettingsChanges = async (): Promise<{ hasChanges: boolean; newHash?: string }> => {
  try {
    console.log('🔍 Checking for ad settings changes...');

    // Check if we've checked recently (within the last 12 hours)
    const now = Date.now();
    const lastCheckTime = await getLastAdSettingsChangeCheckTime();
    const timeSinceLastCheck = now - lastCheckTime;

    if (timeSinceLastCheck < CONTENT_CHECK_DURATION) {
      console.log(`⏰ Ad settings change check throttled (last check was ${DURATION_HELPERS.toHumanReadable(timeSinceLastCheck)} ago)`);
      return { hasChanges: false };
    }

    // Make lightweight API call to check for changes
    const response = await api.get('/admin/ad-settings/check-changes', {
      timeout: 10000 // 10 second timeout for change check
    });

    if (response.data && response.data.hash) {
      const newHash = response.data.hash;
      const storedHash = await getStoredAdSettingsChangeHash();

      console.log('📊 Ad settings change check result:', {
        newHash: newHash.substring(0, 8) + '...',
        storedHash: storedHash ? storedHash.substring(0, 8) + '...' : 'none',
        hasChanges: newHash !== storedHash
      });

      // Update last check time
      await updateLastAdSettingsChangeCheckTime();

      if (newHash !== storedHash) {
        console.log('✅ Ad settings changes detected');
        return { hasChanges: true, newHash };
      } else {
        console.log('📦 No ad settings changes detected');
        return { hasChanges: false };
      }
    } else {
      console.warn('⚠️ Invalid response from ad settings change check API');
      return { hasChanges: true }; // Assume changes if API response is invalid
    }
  } catch (error) {
    console.error('❌ Error checking ad settings changes:', error);
    Sentry.captureException(error);
    // On error, assume changes to ensure ad settings are fetched
    return { hasChanges: true };
  }
};

/**
 * Fetch ad settings from backend with time-based throttling
 */
export const fetchAndUpdateAdSettings = async (forceRefresh: boolean = false): Promise<AdSettings> => {
  // Prevent concurrent calls unless forceRefresh is true
  if (!forceRefresh && ongoingFetchPromise) {
    console.log('🔄 Ad settings fetch already in progress, returning existing promise');
    return ongoingFetchPromise;
  }

  // Create the actual fetch function
  const actualFetch = async (): Promise<AdSettings> => {
    try {
      console.log('🔄 Initiating ad settings update from backend API');
      console.log('🔧 DEBUG: API_URL =', API_URL);

    // Check if we've refreshed settings recently (within the last hour)
    const now = Date.now();
    const lastRefreshTime = await getLastAdSettingsRefreshTime();
    const timeSinceLastRefresh = now - lastRefreshTime;
    console.log('⏱️ Time since last refresh (ms):', timeSinceLastRefresh);
    console.log('🕒 DEBUG: now =', now, ', lastRefreshTime =', lastRefreshTime);

    if (!forceRefresh && timeSinceLastRefresh < 3600000) { // 1 hour threshold
      console.log(`⏱️ AD_SETTINGS: Skipping settings refresh, timeSinceLastRefresh = ${timeSinceLastRefresh} ms`);

      // Return cached settings without making a new API request
      const cachedSettings = await getCachedAdSettings();
      if (cachedSettings) {
        console.log('📦 Using cached ad settings:', cachedSettings);
        return cachedSettings;
      } else {
        console.log('⚠️ No cached ad settings available, forcing refresh');
        forceRefresh = true;
      }
    } else if (forceRefresh) {
      console.log('🔄 Force refresh requested, bypassing time threshold check');
    } else {
      console.log('⏱️ Time threshold passed (', Math.round(timeSinceLastRefresh / 1000),
                 'seconds since last refresh), proceeding with ad settings update');
    }

    // If not throttled and not forced, check for ad settings changes first
    if (!forceRefresh && timeSinceLastRefresh >= 3600000) {
      try {
        const changeCheck = await checkAdSettingsChanges();
        if (!changeCheck.hasChanges) {
          console.log('🔍 No ad settings changes detected, using cached settings');
          const cachedSettings = await getCachedAdSettings();
          if (cachedSettings) {
            console.log('📦 Using cached ad settings after change check:', cachedSettings);
            return cachedSettings;
          }
          console.log('⚠️ No valid cached ad settings available despite no changes detected, forcing refresh');
          forceRefresh = true;
        } else {
          console.log('🔄 Ad settings changes detected, proceeding with full refresh');
        }
      } catch (error) {
        console.error('❌ Error during ad settings change detection, proceeding with full refresh:', error);
        Sentry.captureException(error);
      }
    }

    // Check internet connection
    const networkState = await NetInfo.fetch();
    if (!networkState.isConnected) {
      console.log('No internet connection detected, checking for cached ad settings');

      const cachedSettings = await getCachedAdSettings();
      if (cachedSettings) {
        console.log('📦 Using cached ad settings while offline:', cachedSettings);
        return cachedSettings;
      }

      // No cached settings and no internet - throw error
      console.error('No internet and no cached ad settings available');
      throw new Error('No internet connection and no cached settings available');
    }

    // (Removed: cache check for faster loading after threshold has passed)

    // Fetch from API
    try {
      console.log(`🌐 Fetching ad settings from API: ${API_URL}/admin/ad-settings`);

      const response = await api.get('/admin/ad-settings', {
        timeout: 15000 // 15 second timeout
      });

      console.log('✅ Ad settings API response received:', response.status);

      if (response.data) {
        const settings: AdSettings = {
          watchAdsButtonEnabled: response.data.watchAdsButtonEnabled,
          watchAdsButtonEnabledIOS: response.data.watchAdsButtonEnabledIOS ?? response.data.watchAdsButtonEnabled ?? true,
          watchAdsButtonEnabledAndroid: response.data.watchAdsButtonEnabledAndroid ?? response.data.watchAdsButtonEnabled ?? true,
          requiredAdsCount: response.data.requiredAdsCount,
          freeContentLimit: response.data.freeContentLimit
        };

        console.log('Ad settings fetched from API:', settings);

        // Save to cache
        if (Platform.OS !== 'web') {
          try {
            const saveResult = await saveAdSettingsToStorage(settings);
            if (saveResult) {
              console.log('Ad settings saved to cache');

              // Update the last refresh timestamp
              const timestampUpdated = await updateLastAdSettingsRefreshTime();
              if (timestampUpdated) {
                console.log('✅ Last ad settings refresh timestamp updated successfully');
              } else {
                console.warn('⚠️ Failed to update last ad settings refresh timestamp');
              }

              // Store ad settings hash for change detection
              try {
                const settingsHash = HASH_HELPERS.generateContentHash(settings);
                await storeAdSettingsChangeHash(settingsHash);
                console.log('✅ Ad settings change hash stored successfully');
              } catch (hashError) {
                console.warn('⚠️ Failed to store ad settings change hash:', hashError);
              }
            } else {
              console.warn('Failed to save ad settings to cache');
            }
          } catch (storageError) {
            console.error('Error saving ad settings to cache:', storageError);
            Sentry.captureException(storageError);
          }
        }

        return settings;
      } else {
        console.warn('API returned empty ad settings data');

        // Try to use cached settings as fallback
        const cachedSettings = await getCachedAdSettings();
        if (cachedSettings) {
          console.log('Using cached ad settings due to empty API response');
          return cachedSettings;
        }

        throw new Error('API returned empty data and no cached settings available');
      }
    } catch (error: any) {
      console.error('Error fetching ad settings from API:');
      Sentry.captureException(error);
      if (error.response) {
        console.error('  Status:', error.response?.status);
        console.error('  Message:', error.message);
      } else {
        console.error('  Error details:', error);
      }

      // Try to use cached settings as fallback
      console.log('Checking for cached ad settings due to API error');
      const cachedSettings = await getCachedAdSettings();
      if (cachedSettings) {
        console.log('Using cached ad settings due to API error');
        return cachedSettings;
      }

      // Last resort - throw error
      console.error('No ad settings available from API or cache');
      throw new Error('Failed to load ad settings from API and no cache available');
    }
  } catch (error) {
    console.error('Error in fetchAndUpdateAdSettings:', error);
    Sentry.captureException(error);

    // Even in case of unexpected errors, try to provide cached settings or defaults
    try {
      const cachedSettings = await getCachedAdSettings();
      if (cachedSettings) {
        console.log('Using cached ad settings due to unexpected error');
        return cachedSettings;
      }
    } catch (cacheError) {
      console.error('Error accessing cached ad settings:', cacheError);
      Sentry.captureException(cacheError);
    }

    console.error('Failed to load ad settings due to unexpected error');
    throw new Error('Unexpected error loading ad settings');
  }
  };

  // Set the ongoing promise and handle cleanup
  ongoingFetchPromise = actualFetch();

  try {
    const result = await ongoingFetchPromise;
    return result;
  } finally {
    // Clear the promise when done (success or failure)
    ongoingFetchPromise = null;
  }
};

/**
 * Load ad settings (main function to be called by the app)
 */
export const loadAdSettings = async (): Promise<AdSettings> => {
  try {
    console.log('Loading ad settings using fetchAndUpdateAdSettings');
    const settings = await fetchAndUpdateAdSettings();
    console.log('Ad settings loaded successfully:', settings);
    return settings;
  } catch (error) {
    console.error('Error loading ad settings:', error);
    Sentry.captureException(error);
    throw error;
  }
};
