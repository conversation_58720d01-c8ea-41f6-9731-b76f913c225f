import { Platform } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import { getDeviceLanguage } from './i18nService';
import CryptoJS from 'crypto-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Constants from 'expo-constants';
import api, { API_URL } from './api';
import * as Sentry from '@sentry/react-native';
import { SERVICE_CACHE_DURATIONS, DURATION_HELPERS, HASH_HELPERS } from '../config/cacheConfig';

// Test hash generation on service load
HASH_HELPERS.testHashGeneration();

// TEMPORARY: Clear all stored hashes for testing
HASH_HELPERS.clearAllStoredHashes();

// Keys and constants
// Access the encryption key from EAS environment variables
const API_ENCRYPTION_KEY = Constants.expoConfig?.extra?.apiEncryptionKey || '';

// Add validation to ensure the key is available
if (!API_ENCRYPTION_KEY) {

  console.error('API encryption key not found in EAS configuration');
}

const GAME_CONTENT_STORAGE_KEY = 'taptrap_game_content'; // Key for AsyncStorage
const STORAGE_VERSION_KEY = 'v1'; // Used to identify the storage format version
const LAST_CONTENT_REFRESH_KEY = 'taptrap_last_content_refresh'; // Key for storing the timestamp of the last content refresh
const CONTENT_CHANGE_CHECK_KEY = 'taptrap_content_change_check'; // Key for storing the timestamp of the last change check
const CONTENT_CHANGE_HASH_KEY = 'taptrap_content_change_hash'; // Key for storing content change hash

// Function to decrypt encrypted API responses
export const decryptApiData = (encryptedData: string): any => {
  try {
    console.log('Decrypting API data with encryption key');
    const bytes = CryptoJS.AES.decrypt(encryptedData, API_ENCRYPTION_KEY);
    const decryptedString = bytes.toString(CryptoJS.enc.Utf8);

    try {
      // Try to parse as JSON
      return JSON.parse(decryptedString);
    } catch (parseError) {
      console.error('Error parsing decrypted API data as JSON:', parseError);
      Sentry.captureException(parseError);
      return decryptedString;
    }
  } catch (error) {
    console.error('Error decrypting API data:', error);
    Sentry.captureException(error);
    throw new Error('Failed to decrypt data from API');
  }
};

// Function to encrypt data for local storage - using a more robust approach for React Native compatibility
export const encryptForStorage = (data: any): string => {
  try {
    console.log('Preparing data for secure storage');

    // Convert data to string if it's an object
    const dataString = typeof data === 'object' ? JSON.stringify(data) : String(data);
    console.log('Data converted to string, length:', dataString.length);

    // Use CryptoJS for base64 encoding which handles Unicode characters properly
    // Format: VERSION:BASE64(JSON)
    const wordArray = CryptoJS.enc.Utf8.parse(dataString);
    const base64Encoded = CryptoJS.enc.Base64.stringify(wordArray);
    const storedData = `${STORAGE_VERSION_KEY}:${base64Encoded}`;

    console.log('Data prepared for storage, length:', storedData.length);

    return storedData;
  } catch (error: any) {
    console.error('Error preparing data for storage:', error);
    Sentry.captureException(error);
    const errorMessage = error && typeof error.message === 'string' ? error.message : 'Unknown error';
    throw new Error('Failed to prepare data for storage: ' + errorMessage);
  }
};

// Function to decrypt data from local storage - using a more robust approach for React Native compatibility
export const decryptFromStorage = (storedData: string): any => {
  try {
    console.log('Reading data from secure storage');
    console.log('Stored data length:', storedData.length);

    // Check the version prefix
    if (storedData.startsWith(`${STORAGE_VERSION_KEY}:`)) {
      // Extract the base64 encoded data
      const base64Data = storedData.substring(STORAGE_VERSION_KEY.length + 1); // +1 for the colon

      try {
        // Decode the base64 data using CryptoJS which handles Unicode characters properly
        const wordArray = CryptoJS.enc.Base64.parse(base64Data);
        const decodedString = CryptoJS.enc.Utf8.stringify(wordArray);
        console.log('Successfully decoded data, length:', decodedString.length);

        try {
          // Parse the JSON
          const parsedData = JSON.parse(decodedString);
          console.log('Successfully parsed data as JSON');
          return parsedData;
        } catch (parseError) {
          console.error('Error parsing decoded data as JSON:', parseError);
          Sentry.captureException(parseError);

          // Try to salvage partial data by looking for valid JSON objects
          try {
            console.log('Attempting to salvage partial JSON data...');
            // Look for content structure patterns
            if (decodedString.includes('"questions"') && decodedString.includes('"dares"')) {
              // Try to extract and parse just the valid parts
              const questionsMatch = decodedString.match(/"questions"\s*:\s*({[^}]*})/);
              const daresMatch = decodedString.match(/"dares"\s*:\s*({[^}]*})/);

              if (questionsMatch && daresMatch) {
                console.log('Found partial content structure, attempting to reconstruct');
                // Create a minimal valid structure
                const partialContent = {
                  questions: {},
                  dares: {}
                };
                return partialContent;
              }
            }
          } catch (salvageError) {
            console.error('Failed to salvage partial data:', salvageError);
            Sentry.captureException(salvageError);
          }

          return decodedString; // Return the raw string if it's not valid JSON
        }
      } catch (decodeError) {
        console.error('Error decoding base64 data:', decodeError);
        Sentry.captureException(decodeError);
        // Try alternative decoding method as fallback
        try {
          console.log('Trying alternative decoding method...');
          // Simple base64 decode as fallback
          const fallbackDecoded = atob(base64Data);
          console.log('Alternative decoding succeeded, length:', fallbackDecoded.length);

          try {
            const parsedData = JSON.parse(fallbackDecoded);
            console.log('Successfully parsed fallback decoded data as JSON');
            return parsedData;
          } catch (parseError) {
            console.error('Error parsing fallback decoded data:', parseError);
            Sentry.captureException(parseError);
          }
        } catch (fallbackError) {
          console.error('Alternative decoding failed:', fallbackError);
          Sentry.captureException(fallbackError);
        }

        throw decodeError; // Re-throw if all fallbacks fail
      }
    } else {
      // Unknown format - try to handle legacy formats or corrupted data
      console.warn('Unknown storage format, attempting recovery...');

      // Check if it might be JSON directly
      try {
        const directParse = JSON.parse(storedData);
        if (directParse && typeof directParse === 'object') {
          console.log('Successfully parsed direct JSON from storage');
          return directParse;
        }
      } catch (directParseError) {
        // Not direct JSON, continue with other recovery attempts
      }

      // Check if it's base64 encoded without version prefix
      try {
        const wordArray = CryptoJS.enc.Base64.parse(storedData);
        const decodedString = CryptoJS.enc.Utf8.stringify(wordArray);

        try {
          const parsedData = JSON.parse(decodedString);
          console.log('Successfully parsed data from non-versioned base64');
          return parsedData;
        } catch (parseError) {
          // Not valid JSON after base64 decode
        }
      } catch (base64Error) {
        // Not valid base64
      }

      // If all recovery attempts fail
      throw new Error('Unknown storage format and recovery failed');
    }
  } catch (error: any) {
    console.error('Error reading data from storage:', error);
    Sentry.captureException(error);
    const errorMessage = error && typeof error.message === 'string' ? error.message : 'Unknown error';
    throw new Error('Failed to read data from storage: ' + errorMessage);
  }
};

// Structure for the game content
interface GameContent {
  questions: {
    casual_questions: Array<{ id: string; text_en: string; text_es: string; text_dom: string }>;
    mild_questions: Array<{ id: string; text_en: string; text_es: string; text_dom: string }>;
    spicy_questions: Array<{ id: string; text_en: string; text_es: string; text_dom: string }>;
    no_limits_questions: Array<{ id: string; text_en: string; text_es: string; text_dom: string }>;
    couple_questions: Array<{ id: string; text_en: string; text_es: string; text_dom: string }>;
  };
  dares: {
    casual_dares: Array<{ id: string; text_en: string; text_es: string; text_dom: string }>;
    mild_dares: Array<{ id: string; text_en: string; text_es: string; text_dom: string }>;
    spicy_dares: Array<{ id: string; text_en: string; text_es: string; text_dom: string }>;
    no_limits_dares: Array<{ id: string; text_en: string; text_es: string; text_dom: string }>;
    couple_dares: Array<{ id: string; text_en: string; text_es: string; text_dom: string }>;
  };
}

// Helper function to get text based on language
export const getLocalizedText = (item: { text_en: string; text_es: string; text_dom: string }): string => {
  const language = getDeviceLanguage();
  if (language === 'es') return item.text_es;
  if (language === 'dom') return item.text_dom;
  return item.text_en; // Default to English
};

// Function to save game content to secure storage
export const saveGameContentToStorage = async (content: GameContent): Promise<boolean> => {
  try {
    if (Platform.OS === 'web') {
      console.log('Storage not available on web platform');
      return false;
    }

    // Validate content before saving
    if (!content || !content.questions || !content.dares) {
      console.error('Invalid content structure, not saving to storage');
      return false;
    }

    console.log('Preparing game content for secure storage...');

    // Prepare the content for storage (Base64 encoding)
    const preparedContent = encryptForStorage(content);

    console.log('Content successfully prepared. Length:', preparedContent.length);
    console.log('Content preview:', preparedContent.substring(0, 50) + '...');

    // Save to AsyncStorage
    await AsyncStorage.setItem(GAME_CONTENT_STORAGE_KEY, preparedContent);
    console.log('Game content saved to AsyncStorage with key:', GAME_CONTENT_STORAGE_KEY);

    return true;
  } catch (error) {
    console.error('Error preparing or saving game content to storage:', error);
    Sentry.captureException(error);
    return false;
  }
};

// Default minimal game content structure for fallback
const createEmptyGameContent = (): GameContent => ({
  questions: {
    casual_questions: [],
    mild_questions: [],
    spicy_questions: [],
    no_limits_questions: [],
    couple_questions: []
  },
  dares: {
    casual_dares: [],
    mild_dares: [],
    spicy_dares: [],
    no_limits_dares: [],
    couple_dares: []
  }
});

// Function to get cached game content from storage with improved offline handling
export const getCachedGameContent = async (): Promise<GameContent | null> => {
  try {
    // Only try to read from AsyncStorage on mobile platforms
    if (Platform.OS === 'web') {
      console.log('Storage not available on web platform');
      return null;
    }

    // Check network status to determine how to handle errors
    const networkState = await NetInfo.fetch();
    const isOnline = networkState.isConnected === true;

    // Get stored content from AsyncStorage
    const storedContent = await AsyncStorage.getItem(GAME_CONTENT_STORAGE_KEY);

    if (!storedContent) {
      console.log('No cached content available in AsyncStorage');
      return null;
    }

    try {
      // Read and decode the content
      console.log('Reading cached content from AsyncStorage');
      const decodedContent = decryptFromStorage(storedContent);

      // Verify the decoded content has the expected structure
      if (decodedContent && decodedContent.questions && decodedContent.dares) {
        console.log('Loaded valid cached content from AsyncStorage');
        console.log('- Questions categories:', Object.keys(decodedContent.questions).length);
        console.log('- Dares categories:', Object.keys(decodedContent.dares).length);

        return decodedContent;
      } else {
        console.warn('Decoded content has invalid structure');

        // Different handling based on network status
        if (isOnline) {
          console.log('Online: Will fetch fresh content to replace invalid cache');
          return null; // Return null to trigger a fresh fetch
        } else {
          console.warn('Offline: Attempting to use partial content despite invalid structure');

          // Try to salvage whatever we can from the decoded content
          if (decodedContent && typeof decodedContent === 'object') {
            // Create a valid structure with whatever data we have
            const fallbackContent = createEmptyGameContent();

            // Copy any valid categories we can find
            if (decodedContent.questions) {
              // Type-safe approach to copy valid categories
              if (decodedContent.questions.casual_questions && Array.isArray(decodedContent.questions.casual_questions)) {
                fallbackContent.questions.casual_questions = decodedContent.questions.casual_questions;
              }
              if (decodedContent.questions.mild_questions && Array.isArray(decodedContent.questions.mild_questions)) {
                fallbackContent.questions.mild_questions = decodedContent.questions.mild_questions;
              }
              if (decodedContent.questions.spicy_questions && Array.isArray(decodedContent.questions.spicy_questions)) {
                fallbackContent.questions.spicy_questions = decodedContent.questions.spicy_questions;
              }
              if (decodedContent.questions.no_limits_questions && Array.isArray(decodedContent.questions.no_limits_questions)) {
                fallbackContent.questions.no_limits_questions = decodedContent.questions.no_limits_questions;
              }
              if (decodedContent.questions.couple_questions && Array.isArray(decodedContent.questions.couple_questions)) {
                fallbackContent.questions.couple_questions = decodedContent.questions.couple_questions;
              }
            }

            if (decodedContent.dares) {
              // Type-safe approach to copy valid categories
              if (decodedContent.dares.casual_dares && Array.isArray(decodedContent.dares.casual_dares)) {
                fallbackContent.dares.casual_dares = decodedContent.dares.casual_dares;
              }
              if (decodedContent.dares.mild_dares && Array.isArray(decodedContent.dares.mild_dares)) {
                fallbackContent.dares.mild_dares = decodedContent.dares.mild_dares;
              }
              if (decodedContent.dares.spicy_dares && Array.isArray(decodedContent.dares.spicy_dares)) {
                fallbackContent.dares.spicy_dares = decodedContent.dares.spicy_dares;
              }
              if (decodedContent.dares.no_limits_dares && Array.isArray(decodedContent.dares.no_limits_dares)) {
                fallbackContent.dares.no_limits_dares = decodedContent.dares.no_limits_dares;
              }
              if (decodedContent.dares.couple_dares && Array.isArray(decodedContent.dares.couple_dares)) {
                fallbackContent.dares.couple_dares = decodedContent.dares.couple_dares;
              }
            }

            // Check if we managed to salvage anything
            const totalQuestions = Object.values(fallbackContent.questions)
              .reduce((sum, arr) => sum + arr.length, 0);
            const totalDares = Object.values(fallbackContent.dares)
              .reduce((sum, arr) => sum + arr.length, 0);

            if (totalQuestions > 0 || totalDares > 0) {
              console.log(`Salvaged partial content: ${totalQuestions} questions, ${totalDares} dares`);
              return fallbackContent;
            }
          }

          // If we couldn't salvage anything, return an empty but valid structure
          console.warn('Offline: Using empty content structure as last resort');
          return createEmptyGameContent();
        }
      }
    } catch (readError) {
      console.error('Error reading cached content:', readError);
      Sentry.captureException(readError);

      // Only delete corrupted cache when online
      if (isOnline) {
        console.log('Online: Removing invalid content from AsyncStorage');
        await AsyncStorage.removeItem(GAME_CONTENT_STORAGE_KEY);
        return null; // Will trigger a fresh fetch
      } else {
        console.warn('Offline: Keeping corrupted cache for potential future recovery');

        // When offline, provide an empty but valid structure as fallback
        console.log('Offline: Using empty content structure as fallback');
        return createEmptyGameContent();
      }
    }
  } catch (error) {
    console.error('Error accessing cached game content:', error);
    Sentry.captureException(error);

    // Even in case of unexpected errors, provide a valid structure when offline
    try {
      const networkState = await NetInfo.fetch();
      if (networkState.isConnected !== true) {
        console.warn('Offline with unexpected error: Using empty content structure');
        return createEmptyGameContent();
      }
    } catch (netError) {
      // If we can't even check network status, assume we're offline
      console.warn('Unable to check network status: Using empty content structure');
      return createEmptyGameContent();
    }

    return null;
  }
};

// Function to fetch game content from our API
// Test function to verify API decryption and storage encoding/decoding
export const testDecryption = (): void => {
  try {
    const sampleEncryptedData = `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`;

    // Test API decryption
    console.log('Testing API decryption with sample data...');
    const decryptedApiData = decryptApiData(sampleEncryptedData);
    console.log('API decryption test result:', decryptedApiData ? 'SUCCESS' : 'FAILED');

    if (decryptedApiData) {
      // Log a sample of the decrypted data
      console.log('Sample of decrypted API data:',
        JSON.stringify(decryptedApiData).substring(0, 200) + '...');

      // Test storage encoding/decoding
      console.log('\nTesting storage encoding/decoding...');

      // Create a small test object
      const testObject = {
        test: 'This is a test',
        number: 123,
        nested: {
          value: 'Nested value'
        },
        array: [1, 2, 3]
      };

      // Encode for storage
      const encodedForStorage = encryptForStorage(testObject);
      console.log('Data encoded for storage, length:', encodedForStorage.length);
      console.log('Encoded data preview:', encodedForStorage.substring(0, 50) + '...');

      // Decode from storage
      const decodedFromStorage = decryptFromStorage(encodedForStorage);
      console.log('Storage decoding test result:',
        JSON.stringify(decodedFromStorage) === JSON.stringify(testObject) ? 'SUCCESS' : 'FAILED');

      // Also test with the API data
      console.log('\nTesting with API data...');
      const encodedApiData = encryptForStorage(decryptedApiData);
      console.log('API data encoded for storage, length:', encodedApiData.length);

      const decodedApiData = decryptFromStorage(encodedApiData);
      console.log('API data storage test result:',
        JSON.stringify(decodedApiData) === JSON.stringify(decryptedApiData) ? 'SUCCESS' : 'FAILED');
    }
  } catch (error) {
    console.error('Decryption test failed:', error);
    Sentry.captureException(error);
  }
};

// Helper function to get the timestamp of the last content refresh
export const getLastContentRefreshTime = async (): Promise<number> => {
  try {
    if (Platform.OS === 'web') {
      return 0; // On web, always return 0 to force refresh
    }

    const storedTimestamp = await AsyncStorage.getItem(LAST_CONTENT_REFRESH_KEY);
    if (!storedTimestamp) {
      return 0; // If no timestamp is stored, return 0 to force refresh
    }

    return parseInt(storedTimestamp, 10);
  } catch (error) {
    console.error('Error getting last content refresh time:', error);
    Sentry.captureException(error);
    return 0; // On error, return 0 to force refresh
  }
};

// Helper function to update the timestamp of the last content refresh
export const updateLastContentRefreshTime = async (): Promise<boolean> => {
  try {
    if (Platform.OS === 'web') {
      return false; // Skip on web platform
    }

    const now = Date.now();
    await AsyncStorage.setItem(LAST_CONTENT_REFRESH_KEY, now.toString());
    return true;
  } catch (error) {
    console.error('Error updating last content refresh time:', error);
    Sentry.captureException(error);
    return false;
  }
};

// Content check duration from centralized configuration - smart checking every 12 hours
const CONTENT_CHECK_DURATION = SERVICE_CACHE_DURATIONS.CONTENT;

// Helper function to get the timestamp of the last change check
export const getLastChangeCheckTime = async (): Promise<number> => {
  try {
    if (Platform.OS === 'web') {
      return 0;
    }

    const storedTimestamp = await AsyncStorage.getItem(CONTENT_CHANGE_CHECK_KEY);
    if (!storedTimestamp) {
      return 0;
    }

    return parseInt(storedTimestamp, 10);
  } catch (error) {
    console.error('Error getting last change check time:', error);
    Sentry.captureException(error);
    return 0;
  }
};

// Helper function to update the timestamp of the last change check
export const updateLastChangeCheckTime = async (): Promise<boolean> => {
  try {
    if (Platform.OS === 'web') {
      return false;
    }

    const now = Date.now();
    await AsyncStorage.setItem(CONTENT_CHANGE_CHECK_KEY, now.toString());
    return true;
  } catch (error) {
    console.error('Error updating last change check time:', error);
    Sentry.captureException(error);
    return false;
  }
};

// Helper function to get stored content change hash
export const getStoredContentChangeHash = async (): Promise<string | null> => {
  try {
    if (Platform.OS === 'web') {
      return null;
    }

    const storedHash = await AsyncStorage.getItem(CONTENT_CHANGE_HASH_KEY);

    // DEBUG: Log what we retrieved from storage
    console.log('🔍 RETRIEVED HASH FROM STORAGE:', {
      key: CONTENT_CHANGE_HASH_KEY,
      storedHash,
      hashExists: !!storedHash,
      hashLength: storedHash ? storedHash.length : 0
    });

    return storedHash;
  } catch (error) {
    console.error('Error getting stored content change hash:', error);
    Sentry.captureException(error);
    return null;
  }
};

// Helper function to store content change hash
export const storeContentChangeHash = async (hash: string): Promise<boolean> => {
  try {
    if (Platform.OS === 'web') {
      return false;
    }

    await AsyncStorage.setItem(CONTENT_CHANGE_HASH_KEY, hash);
    return true;
  } catch (error) {
    console.error('Error storing content change hash:', error);
    Sentry.captureException(error);
    return false;
  }
};

// Check if content has changed since last check (lightweight API call)
export const checkContentChanges = async (): Promise<{ hasChanges: boolean; newHash?: string }> => {
  try {
    console.log('🔍 Checking for content changes...');

    // Check if we've checked recently (within the last 12 hours)
    const now = Date.now();
    const lastCheckTime = await getLastChangeCheckTime();
    const timeSinceLastCheck = now - lastCheckTime;

    // TEMPORARY: Disable throttling for testing
    if (false && timeSinceLastCheck < CONTENT_CHECK_DURATION) {
      console.log(`⏰ Content change check throttled (last check was ${DURATION_HELPERS.toHumanReadable(timeSinceLastCheck)} ago)`);
      return { hasChanges: false };
    }

    // Make lightweight API call to check for changes
    const response = await api.get('/content/check-changes', {
      timeout: 10000 // 10 second timeout for change check
    });

    if (response.data && response.data.hash) {
      const newHash = response.data.hash;
      const storedHash = await getStoredContentChangeHash();

      console.log('📊 Content change check result:', {
        newHash: newHash.substring(0, 8) + '...',
        storedHash: storedHash ? storedHash.substring(0, 8) + '...' : 'none',
        hasChanges: newHash !== storedHash
      });

      // DEBUG: Detailed hash comparison
      console.log('🔍 DETAILED HASH COMPARISON:', {
        newHashFull: newHash,
        storedHashFull: storedHash,
        newHashLength: newHash.length,
        storedHashLength: storedHash ? storedHash.length : 0,
        areExactlyEqual: newHash === storedHash,
        storedHashExists: !!storedHash
      });

      // Update last check time
      await updateLastChangeCheckTime();

      if (newHash !== storedHash) {
        console.log('✅ Content changes detected');
        return { hasChanges: true, newHash };
      } else {
        console.log('📦 No content changes detected');
        return { hasChanges: false };
      }
    } else {
      console.warn('⚠️ Invalid response from content change check API');
      return { hasChanges: true }; // Assume changes if API response is invalid
    }
  } catch (error) {
    console.error('❌ Error checking content changes:', error);
    Sentry.captureException(error);
    // On error, assume changes to ensure content is fetched
    return { hasChanges: true };
  }
};

// Utility function to verify and debug the cache content
export const verifyCacheContent = async (): Promise<void> => {
  try {
    if (Platform.OS === 'web') {
      console.log('Cache verification not available on web platform');
      return;
    }

    // Get stored content from AsyncStorage
    const storedContent = await AsyncStorage.getItem(GAME_CONTENT_STORAGE_KEY);

    if (!storedContent) {
      console.log('No cached content exists in AsyncStorage');
      return;
    }

    console.log('Cached content exists in AsyncStorage');
    console.log('Stored content length:', storedContent.length);
    console.log('Stored content preview:', storedContent.substring(0, 50) + '...');

    // Check if it's using our versioned format
    if (storedContent.startsWith(`${STORAGE_VERSION_KEY}:`)) {
      console.log(`Content uses storage format version: ${STORAGE_VERSION_KEY}`);
    } else {
      console.warn('Content does not use the expected storage format');
    }

    try {
      // Try to decode the content
      const decodedContent = decryptFromStorage(storedContent);
      console.log('Successfully decoded content from AsyncStorage');

      // Check the structure of the decoded content
      if (typeof decodedContent === 'object' && decodedContent !== null) {
        console.log('Decoded content is a valid object');
        console.log('Decoded content structure:',
          Object.keys(decodedContent).map(key => `${key}: ${typeof decodedContent[key]}`).join(', ')
        );

        // Check for questions and dares
        if (decodedContent.questions && decodedContent.dares) {
          console.log('Cache has valid game content structure');
          console.log('Questions categories:', Object.keys(decodedContent.questions).join(', '));
          console.log('Dares categories:', Object.keys(decodedContent.dares).join(', '));

          // Count total items
          let totalQuestions = 0;
          let totalDares = 0;

          Object.keys(decodedContent.questions).forEach(category => {
            totalQuestions += decodedContent.questions[category].length;
          });

          Object.keys(decodedContent.dares).forEach(category => {
            totalDares += decodedContent.dares[category].length;
          });

          console.log(`Total questions: ${totalQuestions}, Total dares: ${totalDares}`); //, JSON.stringify(decodedContent)
        } else {
          console.warn('Cache does not have valid game content structure');
        }
      } else {
        console.error('Decoded content is not a valid object:', typeof decodedContent);
      }
    } catch (error) {
      console.error('Error decoding or parsing cached content:', error);
      Sentry.captureException(error);
    }
  } catch (error) {
    console.error('Error verifying cache:', error);
    Sentry.captureException(error);
  }
};

// Function to load game content, using fetchAndUpdateGameContent
export const loadGameContent = async (): Promise<GameContent | null> => {
  try {
    console.log('Loading game content using fetchAndUpdateGameContent');

    // Use fetchAndUpdateGameContent to get content from API or cache
    const result = await fetchAndUpdateGameContent();

    if (result.content) {
      console.log('Game content loaded successfully');
      return result.content;
    } else {
      console.warn('No game content available');
      return null;
    }
  } catch (error) {
    console.error('Error loading game content:', error);
    Sentry.captureException(error);
    return null;
  }
};

// Result type to distinguish between API success, cache due to throttling, and cache due to error
export interface ContentFetchResult {
  content: GameContent | null;
  fromCache: boolean;
  isThrottled: boolean; // true when cache is used due to throttling (not an error)
  apiSuccess: boolean; // true when API call was successful (even if cache was returned initially)
}

// Global promise to prevent concurrent calls
let ongoingFetchPromise: Promise<ContentFetchResult> | null = null;

export const fetchAndUpdateGameContent = async (forceRefresh: boolean = false): Promise<ContentFetchResult> => {
  // Prevent concurrent calls unless forceRefresh is true
  if (!forceRefresh && ongoingFetchPromise) {
    console.log('🔄 Content fetch already in progress, returning existing promise');
    return ongoingFetchPromise;
  }

  // Create the actual fetch function
  const actualFetch = async (): Promise<ContentFetchResult> => {
    try {
      console.log('🔄 Initiating game content update from backend API');

    // Smart content checking - lightweight check every 12 hours, full fetch only if changes detected
    if (!forceRefresh) {
      try {
        console.log('🔍 CONTENT: About to call checkContentChanges()');
        const changeCheck = await checkContentChanges();
        console.log('🔍 CONTENT: checkContentChanges() returned:', changeCheck);
        if (!changeCheck.hasChanges) {
          console.log('🔍 No content changes detected, using cached content');
          const cachedContent = await getCachedGameContent();
          if (cachedContent) {
            const totalQuestions = Object.values(cachedContent.questions)
              .reduce((sum, arr) => sum + arr.length, 0);
            const totalDares = Object.values(cachedContent.dares)
              .reduce((sum, arr) => sum + arr.length, 0);

            if (totalQuestions > 0 || totalDares > 0) {
              console.log(`📦 Using cached content after change check: ${totalQuestions} questions, ${totalDares} dares`);
              return { content: cachedContent, fromCache: true, isThrottled: false, apiSuccess: true };
            }
          }
          console.log('⚠️ No valid cached content available despite no changes detected, forcing refresh');
          forceRefresh = true;
        } else {
          console.log('🔄 Content changes detected, proceeding with full refresh');
        }
      } catch (error) {
        console.error('❌ Error during change detection, proceeding with full refresh:', error);
        Sentry.captureException(error);
      }
    } else {
      console.log('🔄 Force refresh requested, bypassing change detection');
    }

    // Check internet connection
    const networkState = await NetInfo.fetch();
    if (!networkState.isConnected) {
      console.log('No internet connection detected, checking for cached content');

      // Try to load cached content if available - with offline fallback mechanisms
      const cachedContent = await getCachedGameContent();
      if (cachedContent) {
        // Check if the content has any actual data
        const totalQuestions = Object.values(cachedContent.questions)
          .reduce((sum, arr) => sum + arr.length, 0);
        const totalDares = Object.values(cachedContent.dares)
          .reduce((sum, arr) => sum + arr.length, 0);

        if (totalQuestions > 0 || totalDares > 0) {
          console.log(`📦 Using cached content while offline: ${totalQuestions} questions, ${totalDares} dares`);
          return { content: cachedContent, fromCache: true, isThrottled: false, apiSuccess: false };
        } else {
          console.warn('⚠️ Cached content exists but contains no items while offline');
          // Cuando estamos offline y el caché está vacío, aún devolvemos la estructura vacía
          // para evitar mostrar la pantalla NoInternetScreen
          return { content: cachedContent, fromCache: true, isThrottled: false, apiSuccess: false };
        }
      }

      // No cached content and no internet - last resort fallback
      console.log('No internet and no cached content available');
      // Return empty but valid structure to prevent NoInternetScreen when offline
      return { content: createEmptyGameContent(), fromCache: true, isThrottled: false, apiSuccess: false };
    }

    // If we have internet but forceRefresh is false, we might still want to check
    // if we have cached content first for faster loading
    if (!forceRefresh) {
      const cachedContent = await getCachedGameContent();
      if (cachedContent) {
        // Check if the content has any actual data
        const totalQuestions = Object.values(cachedContent.questions)
          .reduce((sum, arr) => sum + arr.length, 0);
        const totalDares = Object.values(cachedContent.dares)
          .reduce((sum, arr) => sum + arr.length, 0);

        if (totalQuestions > 0 || totalDares > 0) {
          console.log(`📦 Using cached content initially (${totalQuestions} questions, ${totalDares} dares), will fetch fresh data`);

          // Instead of background refresh, proceed with the API call immediately
          // This ensures we get fresh data and can properly report API success/failure
          console.log('🔄 Proceeding with immediate API call to get fresh content');
          // Don't return here, continue to the API call section
        } else {
          console.warn('⚠️ Cached content exists but contains no items, forcing immediate refresh');
          // Si el caché está vacío, forzamos un refresh inmediato en lugar de continuar
          forceRefresh = true;
        }
      }
    }

    // With internet connection, try to fetch data from our API
    try {
      console.log(`🌐 Fetching game content from API: ${API_URL}/content`);

      const response = await api.get('/content', {
        timeout: 15000 // 15 second timeout for slower connections
      });

      console.log('✅ API response received:', response.status);


      if (response.data) {
        let gameContent;

        // Check if the response is encrypted
        if (response.data.encrypted === true && response.data.data) {
          console.log('Received encrypted data from API');
          try {
            // Decrypt the data
            gameContent = decryptApiData(response.data.data);
            console.log('Successfully decrypted data from API');

            // Additional validation for decrypted content
            if (!gameContent || typeof gameContent !== 'object') {
              throw new Error('Decryption produced invalid data format');
            }
          } catch (decryptError) {
            console.error('Failed to decrypt data from API:', decryptError);
            Sentry.captureException(decryptError);

            // Try to use cached content as a fallback
            console.log('Checking for cached content due to decryption error');
            const cachedContent = await getCachedGameContent();
            if (cachedContent) {
              console.log('Using cached content due to decryption error');
              return { content: cachedContent, fromCache: true, isThrottled: false, apiSuccess: false };
            }

            return { content: null, fromCache: false, isThrottled: false, apiSuccess: false }; // No data available
          }
        } else {
          // Response is not encrypted
          console.log('Received unencrypted data from API');
          gameContent = response.data;
        }

        // Log the type of content we're working with
        console.log('Game content type:', typeof gameContent,
          Array.isArray(gameContent) ? 'array' :
          (gameContent === null ? 'null' : 'object')
        );

        // Validate the data structure
        if (!gameContent.questions || !gameContent.dares) {
          console.warn('API returned invalid data structure:', JSON.stringify(gameContent).substring(0, 100) + '...');
          return { content: null, fromCache: false, isThrottled: false, apiSuccess: false }; // Invalid data, don't use it
        }

        console.log('Content categories received:');
        console.log('- Questions:', Object.keys(gameContent.questions).map(cat =>
          `${cat}: ${gameContent.questions[cat].length} items`
        ));
        console.log('- Dares:', Object.keys(gameContent.dares).map(cat =>
          `${cat}: ${gameContent.dares[cat].length} items`
        ));

        // Save the updated content to encrypted AsyncStorage
        if (Platform.OS !== 'web') {
          try {
            // Log that we're caching the content
            if (response.data.encrypted === true) {
              console.log('Saving decrypted game content to encrypted AsyncStorage');
            } else {
              console.log('Saving game content to encrypted AsyncStorage');
            }

            // Save the content to AsyncStorage (it will be encrypted by the function)
            const saveResult = await saveGameContentToStorage(gameContent);

            if (saveResult) {
              console.log('Content updated from API and saved to encrypted AsyncStorage');

              // Update the last refresh timestamp
              const timestampUpdated = await updateLastContentRefreshTime();
              if (timestampUpdated) {
                console.log('✅ Last content refresh timestamp updated successfully');
              } else {
                console.warn('⚠️ Failed to update last content refresh timestamp');
              }

              // Store content hash for change detection if available
              try {
                // Generate a simple hash from the content for change detection
                const contentHash = HASH_HELPERS.generateContentHash(gameContent);
                await storeContentChangeHash(contentHash);
                console.log('✅ Content change hash stored successfully');

                // DEBUG: Show what hash was stored
                console.log('🔍 STORED HASH DEBUG:', {
                  hashGenerated: contentHash,
                  hashLength: contentHash.length,
                  hashPreview: contentHash.substring(0, 16) + '...'
                });
              } catch (hashError) {
                console.warn('⚠️ Failed to store content change hash:', hashError);
              }
            } else {
              console.warn('Failed to save content to AsyncStorage');
            }
          } catch (storageError) {
            console.error('Error saving content to AsyncStorage:', storageError);
            Sentry.captureException(storageError);
          }
        } else {
          console.log('Skipping cache update on web platform');
        }

        return { content: gameContent, fromCache: false, isThrottled: false, apiSuccess: true };
      } else {
        console.warn('API returned empty data');
        return { content: null, fromCache: false, isThrottled: false, apiSuccess: false };
      }
    } catch (error: any) {
      console.error('Error fetching data from API:');
      Sentry.captureException(error);
      if (error.response) {
        console.error('  Status:', error.response?.status);
        console.error('  Message:', error.message);
        if (error.response?.data) {
          console.error('  Response data:', error.response.data);
        }
      } else {
        console.error('  Error details:', error);
      }

      // Try to use cached content as a fallback
      console.log('Checking for cached content due to API error');
      const cachedContent = await getCachedGameContent();
      if (cachedContent) {
        console.log('Using cached content due to API error');
        return { content: cachedContent, fromCache: true, isThrottled: false, apiSuccess: false };
      }

      // Last resort fallback - return empty but valid structure
      console.warn('No content available from API or cache, using empty fallback');
      return { content: createEmptyGameContent(), fromCache: true, isThrottled: false, apiSuccess: false };
    }
  } catch (error) {
    console.error('Error in fetchAndUpdateGameContent:', error);
    Sentry.captureException(error);

    // Even in case of unexpected errors, try to provide a valid structure
    try {
      const networkState = await NetInfo.fetch();
      if (networkState.isConnected !== true) {
        console.warn('Offline with unexpected error: Using empty content structure');
        return { content: createEmptyGameContent(), fromCache: true, isThrottled: false, apiSuccess: false };
      }
    } catch (netError) {
      // If we can't even check network status, assume we're offline
      console.warn('Unable to check network status: Using empty content structure');
      return { content: createEmptyGameContent(), fromCache: true, isThrottled: false, apiSuccess: false };
    }

      return { content: null, fromCache: false, isThrottled: false, apiSuccess: false };
    }
  };

  // Set the ongoing promise and execute the fetch
  ongoingFetchPromise = actualFetch();

  try {
    const result = await ongoingFetchPromise;
    return result;
  } finally {
    // Clear the ongoing promise when done
    ongoingFetchPromise = null;
  }
};
