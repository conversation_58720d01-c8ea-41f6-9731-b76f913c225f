/**
 * Cache Configuration for TapTrap Services
 * 
 * Centralized configuration for all caching and throttling durations
 * used across different services in the TapTrap application.
 */

// Cache durations in milliseconds
export const CACHE_DURATIONS = {
  // Smart content checking - lightweight check every 12 hours, full fetch only if changes detected
  CONTENT_CHECK: 12 * 60 * 60 * 1000, // 12 hours

  // Ad watch tracking expiration
  AD_WATCH_TRACKING: 15 * 60 * 1000, // 15 minutes

  // Push notification throttling
  PUSH_NOTIFICATION_THROTTLE: 5 * 60 * 1000, // 5 minutes
} as const;

// Service-specific cache durations - simplified to only use content check
export const SERVICE_CACHE_DURATIONS = {
  // All content services use the same 12-hour smart checking
  CONTENT: CACHE_DURATIONS.CONTENT_CHECK,
  PENALTY: CACHE_DURATIONS.CONTENT_CHECK,
  AD_SETTINGS: CACHE_DURATIONS.CONTENT_CHECK,
  WHATS_NEW: CACHE_DURATIONS.CONTENT_CHECK,

  // Push notification service
  PUSH_NOTIFICATIONS: CACHE_DURATIONS.PUSH_NOTIFICATION_THROTTLE,
} as const;

// Helper functions for duration calculations
export const DURATION_HELPERS = {
  /**
   * Convert milliseconds to human-readable format
   */
  toHumanReadable: (ms: number): string => {
    const hours = Math.floor(ms / (60 * 60 * 1000));
    const minutes = Math.floor((ms % (60 * 60 * 1000)) / (60 * 1000));
    const seconds = Math.floor((ms % (60 * 1000)) / 1000);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  },
  
  /**
   * Check if a timestamp is expired based on duration
   */
  isExpired: (timestamp: number, duration: number): boolean => {
    return (Date.now() - timestamp) > duration;
  },
  
  /**
   * Get time remaining until expiration
   */
  getTimeRemaining: (timestamp: number, duration: number): number => {
    const elapsed = Date.now() - timestamp;
    return Math.max(0, duration - elapsed);
  },
  
  /**
   * Get time since last action in human-readable format
   */
  getTimeSince: (timestamp: number): string => {
    const elapsed = Date.now() - timestamp;
    return DURATION_HELPERS.toHumanReadable(elapsed);
  },
} as const;

// Export individual durations for backward compatibility
export const CONTENT_CHECK_DURATION = SERVICE_CACHE_DURATIONS.CONTENT;
export const AD_WATCH_TRACKING_DURATION = CACHE_DURATIONS.AD_WATCH_TRACKING;
export const PUSH_NOTIFICATION_THROTTLE_DURATION = SERVICE_CACHE_DURATIONS.PUSH_NOTIFICATIONS;

// Type definitions for better TypeScript support
export type CacheDuration = typeof CACHE_DURATIONS[keyof typeof CACHE_DURATIONS];
export type ServiceCacheDuration = typeof SERVICE_CACHE_DURATIONS[keyof typeof SERVICE_CACHE_DURATIONS];

export default {
  CACHE_DURATIONS,
  SERVICE_CACHE_DURATIONS,
  DURATION_HELPERS,
};
