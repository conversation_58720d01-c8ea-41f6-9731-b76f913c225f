/**
 * Cache Configuration for TapTrap Services
 * 
 * Centralized configuration for all caching and throttling durations
 * used across different services in the TapTrap application.
 */

// Cache durations in milliseconds
export const CACHE_DURATIONS = {
  // Content refresh throttling (existing pattern)
  CONTENT_REFRESH: 60 * 60 * 1000, // 1 hour
  
  // Change detection throttling (new optimization)
  CHANGE_CHECK: 12 * 60 * 60 * 1000, // 12 hours
  
  // Ad watch tracking expiration
  AD_WATCH_TRACKING: 15 * 60 * 1000, // 15 minutes
  
  // Push notification throttling
  PUSH_NOTIFICATION_THROTTLE: 5 * 60 * 1000, // 5 minutes
} as const;

// Service-specific cache durations
export const SERVICE_CACHE_DURATIONS = {
  // Content service
  CONTENT: {
    REFRESH: CACHE_DURATIONS.CONTENT_REFRESH,
    CHANGE_CHECK: CACHE_DURATIONS.CHANGE_CHECK,
  },
  
  // Penalty service
  PENALTY: {
    REFRESH: CACHE_DURATIONS.CONTENT_REFRESH,
    CHANGE_CHECK: CACHE_DURATIONS.CHANGE_CHECK,
  },
  
  // Ad settings service
  AD_SETTINGS: {
    REFRESH: CACHE_DURATIONS.CONTENT_REFRESH,
    CHANGE_CHECK: CACHE_DURATIONS.CHANGE_CHECK,
  },
  
  // What's New service
  WHATS_NEW: {
    REFRESH: CACHE_DURATIONS.CONTENT_REFRESH,
    CHANGE_CHECK: CACHE_DURATIONS.CHANGE_CHECK,
  },
  
  // Push notification service
  PUSH_NOTIFICATIONS: {
    THROTTLE: CACHE_DURATIONS.PUSH_NOTIFICATION_THROTTLE,
  },
} as const;

// Helper functions for duration calculations
export const DURATION_HELPERS = {
  /**
   * Convert milliseconds to human-readable format
   */
  toHumanReadable: (ms: number): string => {
    const hours = Math.floor(ms / (60 * 60 * 1000));
    const minutes = Math.floor((ms % (60 * 60 * 1000)) / (60 * 1000));
    const seconds = Math.floor((ms % (60 * 1000)) / 1000);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  },
  
  /**
   * Check if a timestamp is expired based on duration
   */
  isExpired: (timestamp: number, duration: number): boolean => {
    return (Date.now() - timestamp) > duration;
  },
  
  /**
   * Get time remaining until expiration
   */
  getTimeRemaining: (timestamp: number, duration: number): number => {
    const elapsed = Date.now() - timestamp;
    return Math.max(0, duration - elapsed);
  },
  
  /**
   * Get time since last action in human-readable format
   */
  getTimeSince: (timestamp: number): string => {
    const elapsed = Date.now() - timestamp;
    return DURATION_HELPERS.toHumanReadable(elapsed);
  },
} as const;

// Export individual durations for backward compatibility
export const CONTENT_REFRESH_DURATION = SERVICE_CACHE_DURATIONS.CONTENT.REFRESH;
export const CHANGE_CHECK_DURATION = SERVICE_CACHE_DURATIONS.CONTENT.CHANGE_CHECK;
export const AD_WATCH_TRACKING_DURATION = CACHE_DURATIONS.AD_WATCH_TRACKING;
export const PUSH_NOTIFICATION_THROTTLE_DURATION = SERVICE_CACHE_DURATIONS.PUSH_NOTIFICATIONS.THROTTLE;

// Type definitions for better TypeScript support
export type CacheDuration = typeof CACHE_DURATIONS[keyof typeof CACHE_DURATIONS];
export type ServiceCacheDuration = typeof SERVICE_CACHE_DURATIONS[keyof typeof SERVICE_CACHE_DURATIONS];

export default {
  CACHE_DURATIONS,
  SERVICE_CACHE_DURATIONS,
  DURATION_HELPERS,
};
