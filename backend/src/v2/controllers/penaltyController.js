const Penalty = require('../../models/penalty');
const { encrypt, generateHash } = require('../../utils/encryption');

// Format penalties into the structure expected by the app
const formatPenalties = (items) => {
  const penalties = {
    drinking: [],
    physical: [],
    social: [],
    silly: [],
    creative: []
  };

  items.forEach(item => {
    const { id, text_en, text_es, text_dom, category, isPremium, isDefaultFree, isDefaultPremium } = item;

    if (penalties[category]) {
      penalties[category].push({
        id,
        text_en,
        text_es,
        text_dom,
        isPremium: isPremium || false,
        isDefaultFree: isDefaultFree || false,
        isDefaultPremium: isDefaultPremium || false
      });
    }
  });

  return penalties;
};

// Get all penalties formatted for the game
exports.getAllPenalties = async (req, res) => {
  try {
    console.log('Received request for all penalties');

    // Find all active penalty items
    const items = await Penalty.find({ active: true });
    console.log(`Found ${items.length} active penalty items`);

    // Format penalties into game structure
    const formattedPenalties = formatPenalties(items);

    // Log penalty stats for debugging
    let stats = {
      total: 0,
      categories: {}
    };

    // Gather stats on penalties
    Object.keys(formattedPenalties).forEach(category => {
      const count = formattedPenalties[category].length;
      stats.categories[category] = count;
      stats.total += count;
    });

    console.log('Penalty stats:', stats);

    // Check if encryption is enabled
    if (process.env.ENCRYPTION_KEY) {
      try {
        console.log('Encrypting penalty response data with ENCRYPTION_KEY');
        const encryptedData = encrypt(formattedPenalties, process.env.ENCRYPTION_KEY);

        // Send the encrypted penalties
        return res.status(200).json({
          encrypted: true,
          data: encryptedData
        });
      } catch (encryptError) {
        console.error('Error encrypting penalties:', encryptError);
        // If encryption fails, fall back to unencrypted response
        console.log('Falling back to unencrypted penalty response');
      }
    } else {
      console.log('ENCRYPTION_KEY not set, sending unencrypted penalty response');
    }

    // Send unencrypted penalties
    res.status(200).json(formattedPenalties);
  } catch (error) {
    console.error('Error fetching penalties:', error);
    res.status(500).json({
      error: 'Failed to fetch penalties',
      message: error.message
    });
  }
};

// Check for penalty changes (lightweight endpoint for optimization)
exports.checkPenaltyChanges = async (req, res) => {
  try {
    console.log('Checking for penalty changes (lightweight)');

    // Find all active penalty items (same query as getAllPenalties)
    const items = await Penalty.find({ active: true });
    console.log(`Found ${items.length} active penalty items for change check`);

    // Format penalties into game structure (same as getAllPenalties)
    const formattedPenalties = formatPenalties(items);

    // Generate hash for change detection
    const penaltyHash = generateHash(formattedPenalties);
    console.log('Generated penalty hash for change detection:', penaltyHash.substring(0, 8) + '...');

    // Return lightweight response with hash
    res.status(200).json({
      hash: penaltyHash,
      timestamp: new Date().toISOString(),
      itemCount: items.length
    });
  } catch (error) {
    console.error('Error checking penalty changes:', error);
    res.status(500).json({
      error: 'Failed to check penalty changes',
      message: error.message
    });
  }
};
