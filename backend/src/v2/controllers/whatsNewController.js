const WhatsNew = require('../../models/whatsNew');
const { generateHash } = require('../../utils/encryption');

/**
 * Get all What's New logs for mobile app
 * Returns logs sorted by creation date (newest first)
 */
exports.getAllLogs = async (req, res) => {
  try {
    console.log('Fetching all What\'s New logs for mobile app');
    
    const logs = await WhatsNew.find()
      .sort({ createdAt: -1 })
      .select('-__v');
    
    console.log(`Found ${logs.length} What's New logs`);
    
    res.status(200).json(logs);
  } catch (error) {
    console.error('Error fetching What\'s New logs:', error);
    res.status(500).json({ 
      message: 'Error fetching What\'s New logs',
      error: error.message 
    });
  }
};

/**
 * Check if there are new logs since last visit
 * Used for showing the red dot indicator on home screen
 */
exports.checkNewLogs = async (req, res) => {
  try {
    const { lastVisit } = req.query;
    
    console.log('Checking for new What\'s New logs since:', lastVisit);
    
    let query = {};
    
    // If lastVisit is provided, check for logs created after that time
    if (lastVisit) {
      const lastVisitDate = new Date(lastVisit);
      if (!isNaN(lastVisitDate.getTime())) {
        query.createdAt = { $gt: lastVisitDate };
      }
    }
    
    const newLogsCount = await WhatsNew.countDocuments(query);
    const hasNewLogs = newLogsCount > 0;
    
    console.log(`Found ${newLogsCount} new logs since last visit`);
    
    res.status(200).json({
      hasNewLogs,
      newLogsCount
    });
  } catch (error) {
    console.error('Error checking for new What\'s New logs:', error);
    res.status(500).json({ 
      message: 'Error checking for new logs',
      error: error.message 
    });
  }
};

/**
 * Get all What's New logs for admin panel
 * Includes additional metadata for admin management
 */
exports.getAllLogsAdmin = async (req, res) => {
  try {
    console.log('Fetching all What\'s New logs for admin panel');
    
    const logs = await WhatsNew.find()
      .sort({ createdAt: -1 });
    
    console.log(`Found ${logs.length} What's New logs for admin`);
    
    res.status(200).json(logs);
  } catch (error) {
    console.error('Error fetching What\'s New logs for admin:', error);
    res.status(500).json({ 
      message: 'Error fetching What\'s New logs',
      error: error.message 
    });
  }
};

/**
 * Create new What's New log (admin only)
 */
exports.createLog = async (req, res) => {
  try {
    const {
      type,
      date,
      title_en,
      title_es,
      title_dom,
      description_en,
      description_es,
      description_dom,
      videoUrl,
      appVersion,
      platform,
      forceUpdate
    } = req.body;

    console.log('Creating new What\'s New log:', { type, title_en, appVersion, platform, forceUpdate });

    // Validate required fields
    if (!type || !date || !title_en || !title_es || !title_dom ||
        !description_en || !description_es || !description_dom || !appVersion) {
      return res.status(400).json({
        message: 'Missing required fields: type, date, title_en, title_es, title_dom, description_en, description_es, description_dom, appVersion'
      });
    }

    // Validate type enum
    const validTypes = ['dark', 'red', 'cyan', 'purple'];
    if (!validTypes.includes(type)) {
      return res.status(400).json({
        message: `Invalid type. Must be one of: ${validTypes.join(', ')}`
      });
    }

    // Validate platform enum
    const validPlatforms = ['ios', 'android', 'both'];
    if (platform && !validPlatforms.includes(platform)) {
      return res.status(400).json({
        message: `Invalid platform. Must be one of: ${validPlatforms.join(', ')}`
      });
    }

    // Create new log
    const newLog = new WhatsNew({
      type,
      date,
      title_en,
      title_es,
      title_dom,
      description_en,
      description_es,
      description_dom,
      videoUrl: videoUrl || undefined,
      appVersion,
      platform: platform || 'both',
      forceUpdate: forceUpdate !== undefined ? forceUpdate : false
    });

    console.log('Creating log with forceUpdate:', forceUpdate);

    const savedLog = await newLog.save();

    console.log('What\'s New log created successfully:', savedLog._id);

    res.status(201).json(savedLog);
  } catch (error) {
    console.error('Error creating What\'s New log:', error);
    res.status(500).json({
      message: 'Error creating What\'s New log',
      error: error.message
    });
  }
};

/**
 * Update existing What's New log (admin only)
 */
exports.updateLog = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      type,
      date,
      title_en,
      title_es,
      title_dom,
      description_en,
      description_es,
      description_dom,
      videoUrl,
      appVersion,
      platform,
      forceUpdate
    } = req.body;

    console.log('Updating What\'s New log:', id, { forceUpdate });

    // Validate type enum if provided
    if (type) {
      const validTypes = ['dark', 'red', 'cyan', 'purple'];
      if (!validTypes.includes(type)) {
        return res.status(400).json({
          message: `Invalid type. Must be one of: ${validTypes.join(', ')}`
        });
      }
    }

    // Validate platform enum if provided
    if (platform) {
      const validPlatforms = ['ios', 'android', 'both'];
      if (!validPlatforms.includes(platform)) {
        return res.status(400).json({
          message: `Invalid platform. Must be one of: ${validPlatforms.join(', ')}`
        });
      }
    }

    // Find and update the log
    const updateData = {};
    if (type) updateData.type = type;
    if (date) updateData.date = date;
    if (title_en) updateData.title_en = title_en;
    if (title_es) updateData.title_es = title_es;
    if (title_dom) updateData.title_dom = title_dom;
    if (description_en) updateData.description_en = description_en;
    if (description_es) updateData.description_es = description_es;
    if (description_dom) updateData.description_dom = description_dom;
    if (videoUrl !== undefined) updateData.videoUrl = videoUrl;
    if (appVersion) updateData.appVersion = appVersion;
    if (platform) updateData.platform = platform;
    if (forceUpdate !== undefined) updateData.forceUpdate = forceUpdate; // Handle boolean properly

    console.log('Update data:', updateData);

    const updatedLog = await WhatsNew.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    );

    if (!updatedLog) {
      return res.status(404).json({ message: 'What\'s New log not found' });
    }

    console.log('What\'s New log updated successfully:', updatedLog._id);

    res.status(200).json(updatedLog);
  } catch (error) {
    console.error('Error updating What\'s New log:', error);
    res.status(500).json({
      message: 'Error updating What\'s New log',
      error: error.message
    });
  }
};

/**
 * Delete What's New log (admin only)
 */
exports.deleteLog = async (req, res) => {
  try {
    const { id } = req.params;
    
    console.log('Deleting What\'s New log:', id);
    
    const deletedLog = await WhatsNew.findByIdAndDelete(id);
    
    if (!deletedLog) {
      return res.status(404).json({ message: 'What\'s New log not found' });
    }
    
    console.log('What\'s New log deleted successfully:', deletedLog._id);
    
    res.status(200).json({ 
      message: 'What\'s New log deleted successfully',
      deletedLog 
    });
  } catch (error) {
    console.error('Error deleting What\'s New log:', error);
    res.status(500).json({
      message: 'Error deleting What\'s New log',
      error: error.message
    });
  }
};

// Check for What's New changes (lightweight endpoint for optimization)
exports.checkWhatsNewChanges = async (req, res) => {
  try {
    console.log('Checking for What\'s New changes (lightweight)');

    // Get all logs (same query as getAllLogs)
    const logs = await WhatsNew.find()
      .sort({ createdAt: -1 })
      .select('-__v');

    console.log(`Found ${logs.length} What's New logs for change check`);

    // Generate hash for change detection
    const logsHash = generateHash(logs);
    console.log('Generated What\'s New hash for change detection:', logsHash.substring(0, 8) + '...');

    // Return lightweight response with hash
    res.status(200).json({
      hash: logsHash,
      timestamp: new Date().toISOString(),
      logCount: logs.length
    });
  } catch (error) {
    console.error('Error checking What\'s New changes:', error);
    res.status(500).json({
      message: 'Error checking What\'s New changes',
      error: error.message
    });
  }
};
