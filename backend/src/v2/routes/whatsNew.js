const express = require('express');
const router = express.Router();
const whatsNewController = require('../controllers/whatsNewController');
const authMiddleware = require('../../middleware/auth');

/**
 * @swagger
 * tags:
 *   name: WhatsNew
 *   description: What's New logs management
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     WhatsNewLog:
 *       type: object
 *       required:
 *         - type
 *         - date
 *         - title
 *         - description
 *         - appVersion
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the log
 *         type:
 *           type: string
 *           enum: [dark, red, cyan, purple]
 *           description: Visual theme type for the log card
 *         date:
 *           type: string
 *           description: Display date for the log
 *         title:
 *           type: string
 *           description: Title of the What's New log
 *         description:
 *           type: string
 *           description: Description of the new feature or update
 *         videoUrl:
 *           type: string
 *           description: Optional video URL for demonstration
 *         appVersion:
 *           type: string
 *           description: Minimum app version required for this feature
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 */

/**
 * @swagger
 * /api/whatsnew:
 *   get:
 *     summary: Get all What's New logs for mobile app
 *     tags: [WhatsNew]
 *     responses:
 *       200:
 *         description: List of What's New logs
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/WhatsNewLog'
 *       500:
 *         description: Server error
 */
router.get('/', whatsNewController.getAllLogs);

/**
 * @swagger
 * /api/whatsnew/check-new:
 *   get:
 *     summary: Check if there are new logs since last visit
 *     tags: [WhatsNew]
 *     parameters:
 *       - in: query
 *         name: lastVisit
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Last visit timestamp
 *     responses:
 *       200:
 *         description: Information about new logs
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 hasNewLogs:
 *                   type: boolean
 *                   description: Whether there are new logs since last visit
 *                 newLogsCount:
 *                   type: integer
 *                   description: Number of new logs
 *       500:
 *         description: Server error
 */
router.get('/check-new', whatsNewController.checkNewLogs);

// Protected admin routes
router.use('/admin', authMiddleware);

/**
 * @swagger
 * /api/whatsnew/admin:
 *   post:
 *     summary: Create new What's New log (admin only)
 *     tags: [WhatsNew]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/WhatsNewLog'
 *     responses:
 *       201:
 *         description: What's New log created successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/admin', whatsNewController.createLog);

/**
 * @swagger
 * /api/whatsnew/admin/{id}:
 *   put:
 *     summary: Update existing What's New log (admin only)
 *     tags: [WhatsNew]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Log ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/WhatsNewLog'
 *     responses:
 *       200:
 *         description: What's New log updated successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Log not found
 *       500:
 *         description: Server error
 */
router.put('/admin/:id', whatsNewController.updateLog);

/**
 * @swagger
 * /api/whatsnew/admin/{id}:
 *   delete:
 *     summary: Delete What's New log (admin only)
 *     tags: [WhatsNew]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Log ID
 *     responses:
 *       200:
 *         description: What's New log deleted successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Log not found
 *       500:
 *         description: Server error
 */
router.delete('/admin/:id', whatsNewController.deleteLog);

/**
 * @swagger
 * /api/whatsnew/admin:
 *   get:
 *     summary: Get all What's New logs for admin panel
 *     tags: [WhatsNew]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of What's New logs with admin details
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/admin', whatsNewController.getAllLogsAdmin);

module.exports = router;
